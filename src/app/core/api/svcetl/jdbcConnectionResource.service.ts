/**
 * Api Documentation
 * Api Documentation
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import {Inject, Injectable, Optional} from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse, HttpEvent } from '@angular/common/http';

import {Observable} from 'rxjs';

import {BASE_PATH} from '../variables';
import {Configuration} from '../configuration';
import {JdbcConnectionDTO} from '@model/svcetl/jdbcConnectionDTO';
import {Constants} from "../constants";


@Injectable({
  providedIn: 'root',
})
export class JdbcConnectionResourceService {

  protected basePath = Constants.SVCETL_URL;
  public defaultHeaders = new HttpHeaders();
  public configuration = new Configuration();

  constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
    if (basePath) {
      this.basePath = basePath;
    }
    if (configuration) {
      this.configuration = configuration;
      this.basePath = basePath || configuration.basePath || this.basePath;
    }
  }

  /**
   * createJdbcConnection
   *
   * @param X_TENANT_ID Space Id
   * @param jdbcConnectionDTO jdbcConnectionDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public createJdbcConnectionUsingPOST(X_TENANT_ID: string, jdbcConnectionDTO: JdbcConnectionDTO, observe?: 'body', reportProgress?: boolean): Observable<JdbcConnectionDTO>;
  public createJdbcConnectionUsingPOST(X_TENANT_ID: string, jdbcConnectionDTO: JdbcConnectionDTO, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<JdbcConnectionDTO>>;
  public createJdbcConnectionUsingPOST(X_TENANT_ID: string, jdbcConnectionDTO: JdbcConnectionDTO, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<JdbcConnectionDTO>>;
  public createJdbcConnectionUsingPOST(X_TENANT_ID: string, jdbcConnectionDTO: JdbcConnectionDTO, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (X_TENANT_ID === null || X_TENANT_ID === undefined) {
      throw new Error('Required parameter X_TENANT_ID was null or undefined when calling createJdbcConnectionUsingPOST.');
    }

    if (jdbcConnectionDTO === null || jdbcConnectionDTO === undefined) {
      throw new Error('Required parameter jdbcConnectionDTO was null or undefined when calling createJdbcConnectionUsingPOST.');
    }

    let headers = this.defaultHeaders;
    if (X_TENANT_ID !== undefined && X_TENANT_ID !== null) {
      headers = headers.set('X-TENANT-ID', String(X_TENANT_ID));
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      '*/*'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/json'
    ];
    const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers = headers.set('Content-Type', httpContentTypeSelected);
    }

    return this.httpClient.post<JdbcConnectionDTO>(`${this.basePath}/api/jdbc-connections`,
      jdbcConnectionDTO,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }


  /**
   * getJdbcConnection
   *
   * @param X_TENANT_ID Space Id
   * @param id id
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getJdbcConnectionUsingGET(X_TENANT_ID: string, id: number, observe?: 'body', reportProgress?: boolean): Observable<JdbcConnectionDTO>;
  public getJdbcConnectionUsingGET(X_TENANT_ID: string, id: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<JdbcConnectionDTO>>;
  public getJdbcConnectionUsingGET(X_TENANT_ID: string, id: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<JdbcConnectionDTO>>;
  public getJdbcConnectionUsingGET(X_TENANT_ID: string, id: number, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (X_TENANT_ID === null || X_TENANT_ID === undefined) {
      throw new Error('Required parameter X_TENANT_ID was null or undefined when calling getJdbcConnectionUsingGET.');
    }

    if (id === null || id === undefined) {
      throw new Error('Required parameter id was null or undefined when calling getJdbcConnectionUsingGET.');
    }

    let headers = this.defaultHeaders;
    if (X_TENANT_ID !== undefined && X_TENANT_ID !== null) {
      headers = headers.set('X-TENANT-ID', String(X_TENANT_ID));
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      '*/*'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    return this.httpClient.get<JdbcConnectionDTO>(`${this.basePath}/api/jdbc-connections/${encodeURIComponent(String(id))}`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }



  /**
   * validateConnectionForDTO
   *
   * @param X_TENANT_ID Space Id
   * @param jdbcConnectionDTO jdbcConnectionDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public validateConnectionForDTOUsingPATCH(X_TENANT_ID: string, jdbcConnectionDTO: JdbcConnectionDTO, observe?: 'body', reportProgress?: boolean): Observable<boolean>;
  public validateConnectionForDTOUsingPATCH(X_TENANT_ID: string, jdbcConnectionDTO: JdbcConnectionDTO, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<boolean>>;
  public validateConnectionForDTOUsingPATCH(X_TENANT_ID: string, jdbcConnectionDTO: JdbcConnectionDTO, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<boolean>>;
  public validateConnectionForDTOUsingPATCH(X_TENANT_ID: string, jdbcConnectionDTO: JdbcConnectionDTO, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (X_TENANT_ID === null || X_TENANT_ID === undefined) {
      throw new Error('Required parameter X_TENANT_ID was null or undefined when calling validateConnectionForDTOUsingPATCH.');
    }

    if (jdbcConnectionDTO === null || jdbcConnectionDTO === undefined) {
      throw new Error('Required parameter jdbcConnectionDTO was null or undefined when calling validateConnectionForDTOUsingPATCH.');
    }

    let headers = this.defaultHeaders;
    if (X_TENANT_ID !== undefined && X_TENANT_ID !== null) {
      headers = headers.set('X-TENANT-ID', String(X_TENANT_ID));
    }

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      '*/*'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/json'
    ];
    const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers = headers.set('Content-Type', httpContentTypeSelected);
    }

    return this.httpClient.patch<boolean>(`${this.basePath}/api/jdbc-connections/validate`,
      jdbcConnectionDTO,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

}
