/**
 * Api Documentation
 * Api Documentation
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */ /* tslint:disable:no-unused-variable member-ordering */

 import { Inject, Injectable, Optional } from '@angular/core';
 import { HttpClient, HttpHeaders, HttpParams, HttpResponse, HttpEvent } from '@angular/common/http';
 import { CustomHttpUrlEncodingCodec } from '../encoder';

 import { Observable } from 'rxjs';

 import { UserObjectResponsibilityMetaDataDTO } from '@model/svcdao/userObjectResponsibilityMetaDataDTO';

 import { BASE_PATH } from '../variables';
 import { Configuration } from '../configuration';
 import { Constants } from '../constants';

 @Injectable()
 export class UserObjectResponsibilityMetaDataResourceService {
     protected basePath = Constants.SVCDAO_URL;
     public defaultHeaders = new HttpHeaders();
     public configuration = new Configuration();

     constructor(
         protected httpClient: HttpClient,
         @Optional() @Inject(BASE_PATH) basePath: string,
         @Optional() configuration: Configuration
     ) {
         if (basePath) {
             this.basePath = basePath;
         }
         if (configuration) {
             this.configuration = configuration;
             this.basePath = basePath || configuration.basePath || this.basePath;
         }
     }

     /**
      * createUserObjectResponsibilityMetaData
      *
      * @param body dto
      * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
      * @param reportProgress flag to report request and response progress.
      */
     public createUserObjectResponsibilityMetaDataUsingPOST(
         body: UserObjectResponsibilityMetaDataDTO,
         observe?: 'body',
         reportProgress?: boolean
     ): Observable<UserObjectResponsibilityMetaDataDTO>;
     public createUserObjectResponsibilityMetaDataUsingPOST(
         body: UserObjectResponsibilityMetaDataDTO,
         observe?: 'response',
         reportProgress?: boolean
     ): Observable<HttpResponse<UserObjectResponsibilityMetaDataDTO>>;
     public createUserObjectResponsibilityMetaDataUsingPOST(
         body: UserObjectResponsibilityMetaDataDTO,
         observe?: 'events',
         reportProgress?: boolean
     ): Observable<HttpEvent<UserObjectResponsibilityMetaDataDTO>>;
     public createUserObjectResponsibilityMetaDataUsingPOST(
         body: UserObjectResponsibilityMetaDataDTO,
         observe: any = 'body',
         reportProgress: boolean = false
     ): Observable<any> {
         if (body === null || body === undefined) {
             throw new Error(
                 'Required parameter body was null or undefined when calling createUserObjectResponsibilityMetaDataUsingPOST.'
             );
         }

         let headers = this.defaultHeaders;

         // to determine the Accept header
         let httpHeaderAccepts: string[] = ['*/*'];
         const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
         if (httpHeaderAcceptSelected != undefined) {
             headers = headers.set('Accept', httpHeaderAcceptSelected);
         }

         // to determine the Content-Type header
         const consumes: string[] = ['application/json'];
         const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
         if (httpContentTypeSelected != undefined) {
             headers = headers.set('Content-Type', httpContentTypeSelected);
         }

         return this.httpClient.request<UserObjectResponsibilityMetaDataDTO>(
             'post',
             `${this.basePath}/api/user-object-responsibility-meta-data`,
             {
                 body: body,
                 withCredentials: this.configuration.withCredentials,
                 headers: headers,
                 observe: observe,
                 reportProgress: reportProgress,
             }
         );
     }

     /**
      * deleteUserObjectResponsibilityMetaData
      *
      * @param id id
      * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
      * @param reportProgress flag to report request and response progress.
      */
     public deleteUserObjectResponsibilityMetaDataUsingDELETE(
         id: number,
         observe?: 'body',
         reportProgress?: boolean
     ): Observable<any>;
     public deleteUserObjectResponsibilityMetaDataUsingDELETE(
         id: number,
         observe?: 'response',
         reportProgress?: boolean
     ): Observable<HttpResponse<any>>;
     public deleteUserObjectResponsibilityMetaDataUsingDELETE(
         id: number,
         observe?: 'events',
         reportProgress?: boolean
     ): Observable<HttpEvent<any>>;
     public deleteUserObjectResponsibilityMetaDataUsingDELETE(
         id: number,
         observe: any = 'body',
         reportProgress: boolean = false
     ): Observable<any> {
         if (id === null || id === undefined) {
             throw new Error(
                 'Required parameter id was null or undefined when calling deleteUserObjectResponsibilityMetaDataUsingDELETE.'
             );
         }

         let headers = this.defaultHeaders;

         // to determine the Accept header
         let httpHeaderAccepts: string[] = [];
         const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
         if (httpHeaderAcceptSelected != undefined) {
             headers = headers.set('Accept', httpHeaderAcceptSelected);
         }

         // to determine the Content-Type header
         const consumes: string[] = [];

         return this.httpClient.request<any>(
             'delete',
             `${this.basePath}/api/user-object-responsibility-meta-data/${encodeURIComponent(String(id))}`,
             {
                 withCredentials: this.configuration.withCredentials,
                 headers: headers,
                 observe: observe,
                 reportProgress: reportProgress,
             }
         );
     }
     /**
      * deleteUserObjectResponsibilityMetaData
      *
      * @param id id
      * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
      * @param reportProgress flag to report request and response progress.
      */
     public deleteMultiUserObjectResponsibilityMetaDataUsingDELETE(
         ids: string,
         observe?: 'body',
         reportProgress?: boolean
     ): Observable<any>;
     public deleteMultiUserObjectResponsibilityMetaDataUsingDELETE(
         ids: string,
         observe?: 'response',
         reportProgress?: boolean
     ): Observable<HttpResponse<any>>;
     public deleteMultiUserObjectResponsibilityMetaDataUsingDELETE(
         ids: string,
         observe?: 'events',
         reportProgress?: boolean
     ): Observable<HttpEvent<any>>;
     public deleteMultiUserObjectResponsibilityMetaDataUsingDELETE(
         ids: string,
         observe: any = 'body',
         reportProgress: boolean = false
     ): Observable<any> {
         if (ids === null || ids === undefined) {
             throw new Error(
                 'Required parameter id was null or undefined when calling deleteMultiUserObjectResponsibilityMetaDataUsingDELETE.'
             );
         }

         let headers = this.defaultHeaders;

         // to determine the Accept header
         let httpHeaderAccepts: string[] = [];
         const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
         if (httpHeaderAcceptSelected != undefined) {
             headers = headers.set('Accept', httpHeaderAcceptSelected);
         }

         // to determine the Content-Type header
         const consumes: string[] = [];

         return this.httpClient.request<any>(
             'delete',
             `${this.basePath}/api/user-object-responsibility-meta-data/?${ids}`,
             {
                 withCredentials: this.configuration.withCredentials,
                 headers: headers,
                 observe: observe,
                 reportProgress: reportProgress,
             }
         );
     }

     /**
      * getAllUserObjectResponsibilityMetaDataRSQLCount
      *
      * @param search search
      * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
      * @param reportProgress flag to report request and response progress.
      */
     public getAllUserObjectResponsibilityMetaDataRSQLCountUsingGET(
         search: string,
         observe?: 'body',
         reportProgress?: boolean
     ): Observable<number>;
     public getAllUserObjectResponsibilityMetaDataRSQLCountUsingGET(
         search: string,
         observe?: 'response',
         reportProgress?: boolean
     ): Observable<HttpResponse<number>>;
     public getAllUserObjectResponsibilityMetaDataRSQLCountUsingGET(
         search: string,
         observe?: 'events',
         reportProgress?: boolean
     ): Observable<HttpEvent<number>>;
     public getAllUserObjectResponsibilityMetaDataRSQLCountUsingGET(
         search: string,
         observe: any = 'body',
         reportProgress: boolean = false
     ): Observable<any> {
         if (search === null || search === undefined) {
             throw new Error(
                 'Required parameter search was null or undefined when calling getAllUserObjectResponsibilityMetaDataRSQLCountUsingGET.'
             );
         }

         let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
         if (search !== undefined && search !== null) {
             queryParameters = queryParameters.set('search', <any>search);
         }

         let headers = this.defaultHeaders;

         // to determine the Accept header
         let httpHeaderAccepts: string[] = ['*/*'];
         const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
         if (httpHeaderAcceptSelected != undefined) {
             headers = headers.set('Accept', httpHeaderAcceptSelected);
         }

         // to determine the Content-Type header
         const consumes: string[] = [];

         return this.httpClient.request<number>(
             'get',
             `${this.basePath}/api/user-object-responsibility-meta-data-rsql-count`,
             {
                 params: queryParameters,
                 withCredentials: this.configuration.withCredentials,
                 headers: headers,
                 observe: observe,
                 reportProgress: reportProgress,
             }
         );
     }

     /**
      * getAllUserObjectResponsibilityMetaDataRSQL
      *
      * @param search search
      * @param offset
      * @param page Page number of the requested page
      * @param pageNumber
      * @param pageSize
      * @param paged
      * @param size Size of a page
      * @param sort Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
      * @param sortSorted
      * @param sortUnsorted
      * @param unpaged
      * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
      * @param reportProgress flag to report request and response progress.
      */
     public getAllUserObjectResponsibilityMetaDataRSQLUsingGET(
         search: string,
         page?: number,
         size?: number,
         sort?: Array<string>,
         observe?: 'body',
         reportProgress?: boolean
     ): Observable<Array<UserObjectResponsibilityMetaDataDTO>>;
     public getAllUserObjectResponsibilityMetaDataRSQLUsingGET(
         search: string,
         page?: number,
         size?: number,
         sort?: Array<string>,
         observe?: 'response',
         reportProgress?: boolean
     ): Observable<HttpResponse<Array<UserObjectResponsibilityMetaDataDTO>>>;
     public getAllUserObjectResponsibilityMetaDataRSQLUsingGET(
         search: string,
         page?: number,
         size?: number,
         sort?: Array<string>,
         observe?: 'events',
         reportProgress?: boolean
     ): Observable<HttpEvent<Array<UserObjectResponsibilityMetaDataDTO>>>;
     public getAllUserObjectResponsibilityMetaDataRSQLUsingGET(
         search: string,
         page?: number,
         size?: number,
         sort?: Array<string>,
         observe: any = 'body',
         reportProgress: boolean = false
     ): Observable<any> {
         if (search === null || search === undefined) {
             throw new Error(
                 'Required parameter search was null or undefined when calling getAllUserObjectResponsibilityMetaDataRSQLUsingGET.'
             );
         }

         let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });

         if (page !== undefined && page !== null) {
             queryParameters = queryParameters.set('page', <any>page);
         }

         if (search !== undefined && search !== null) {
             queryParameters = queryParameters.set('search', <any>search);
         }
         if (size !== undefined && size !== null) {
             queryParameters = queryParameters.set('size', <any>size);
         }
         if (sort) {
             sort.forEach((element) => {
                 queryParameters = queryParameters.append('sort', <any>element);
             });
         }
         let headers = this.defaultHeaders;

         // to determine the Accept header
         let httpHeaderAccepts: string[] = ['*/*'];
         const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
         if (httpHeaderAcceptSelected != undefined) {
             headers = headers.set('Accept', httpHeaderAcceptSelected);
         }

         // to determine the Content-Type header
         const consumes: string[] = [];

         return this.httpClient.request<Array<UserObjectResponsibilityMetaDataDTO>>(
             'get',
             `${this.basePath}/api/user-object-responsibility-meta-data-rsql`,
             {
                 params: queryParameters,
                 withCredentials: this.configuration.withCredentials,
                 headers: headers,
                 observe: observe,
                 reportProgress: reportProgress,
             }
         );
     }

   
     /**
      * updateUserObjectResponsibilityMetaData
      *
      * @param body dto
      * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
      * @param reportProgress flag to report request and response progress.
      */
     public updateUserObjectResponsibilityMetaDataUsingPUT(
         body: UserObjectResponsibilityMetaDataDTO,
         observe?: 'body',
         reportProgress?: boolean
     ): Observable<UserObjectResponsibilityMetaDataDTO>;
     public updateUserObjectResponsibilityMetaDataUsingPUT(
         body: UserObjectResponsibilityMetaDataDTO,
         observe?: 'response',
         reportProgress?: boolean
     ): Observable<HttpResponse<UserObjectResponsibilityMetaDataDTO>>;
     public updateUserObjectResponsibilityMetaDataUsingPUT(
         body: UserObjectResponsibilityMetaDataDTO,
         observe?: 'events',
         reportProgress?: boolean
     ): Observable<HttpEvent<UserObjectResponsibilityMetaDataDTO>>;
     public updateUserObjectResponsibilityMetaDataUsingPUT(
         body: UserObjectResponsibilityMetaDataDTO,
         observe: any = 'body',
         reportProgress: boolean = false
     ): Observable<any> {
         if (body === null || body === undefined) {
             throw new Error(
                 'Required parameter body was null or undefined when calling updateUserObjectResponsibilityMetaDataUsingPUT.'
             );
         }

         let headers = this.defaultHeaders;

         // to determine the Accept header
         let httpHeaderAccepts: string[] = ['*/*'];
         const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
         if (httpHeaderAcceptSelected != undefined) {
             headers = headers.set('Accept', httpHeaderAcceptSelected);
         }

         // to determine the Content-Type header
         const consumes: string[] = ['application/json'];
         const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
         if (httpContentTypeSelected != undefined) {
             headers = headers.set('Content-Type', httpContentTypeSelected);
         }

         return this.httpClient.request<UserObjectResponsibilityMetaDataDTO>(
             'put',
             `${this.basePath}/api/user-object-responsibility-meta-data`,
             {
                 body: body,
                 withCredentials: this.configuration.withCredentials,
                 headers: headers,
                 observe: observe,
                 reportProgress: reportProgress,
             }
         );
     }


   /**
    * getAllResponsibilitesWithUsers
    *
    * @param offset
    * @param page Page number of the requested page
    * @param pageNumber
    * @param pageSize
    * @param paged
    * @param size Size of a page
    * @param sort Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
    * @param sortSorted
    * @param sortUnsorted
    * @param unpaged
    * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
    * @param reportProgress flag to report request and response progress.
    */
   public getAllResponsibilitesWithUsersUsingGET(offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'body', reportProgress?: boolean): Observable<Array<UserObjectResponsibilityMetaDataDTO>>;
   public getAllResponsibilitesWithUsersUsingGET(offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<UserObjectResponsibilityMetaDataDTO>>>;
   public getAllResponsibilitesWithUsersUsingGET(offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<UserObjectResponsibilityMetaDataDTO>>>;
   public getAllResponsibilitesWithUsersUsingGET(offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

     let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
     if (offset !== undefined && offset !== null) {
       queryParameters = queryParameters.set('offset', <any>offset);
     }
     if (page !== undefined && page !== null) {
       queryParameters = queryParameters.set('page', <any>page);
     }
     if (pageNumber !== undefined && pageNumber !== null) {
       queryParameters = queryParameters.set('pageNumber', <any>pageNumber);
     }
     if (pageSize !== undefined && pageSize !== null) {
       queryParameters = queryParameters.set('pageSize', <any>pageSize);
     }
     if (paged !== undefined && paged !== null) {
       queryParameters = queryParameters.set('paged', <any>paged);
     }
     if (size !== undefined && size !== null) {
       queryParameters = queryParameters.set('size', <any>size);
     }
     if (sort) {
       sort.forEach((element) => {
         queryParameters = queryParameters.append('sort', <any>element);
       })
     }
     if (sortSorted !== undefined && sortSorted !== null) {
       queryParameters = queryParameters.set('sort.sorted', <any>sortSorted);
     }
     if (sortUnsorted !== undefined && sortUnsorted !== null) {
       queryParameters = queryParameters.set('sort.unsorted', <any>sortUnsorted);
     }
     if (unpaged !== undefined && unpaged !== null) {
       queryParameters = queryParameters.set('unpaged', <any>unpaged);
     }

     let headers = this.defaultHeaders;

     // to determine the Accept header
     let httpHeaderAccepts: string[] = [
       '*/*'
     ];
     const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
     if (httpHeaderAcceptSelected != undefined) {
       headers = headers.set('Accept', httpHeaderAcceptSelected);
     }

     // to determine the Content-Type header
     const consumes: string[] = [
     ];

     return this.httpClient.request<Array<UserObjectResponsibilityMetaDataDTO>>('get',`${this.basePath}/api/user-object-responsibility-meta-data-with-user`,
       {
         params: queryParameters,
         withCredentials: this.configuration.withCredentials,
         headers: headers,
         observe: observe,
         reportProgress: reportProgress
       }
     );
   }
   public getAllResponsibilitesWithUsersRSQLUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'body', reportProgress?: boolean): Observable<Array<UserObjectResponsibilityMetaDataDTO>>;
   public getAllResponsibilitesWithUsersRSQLUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<UserObjectResponsibilityMetaDataDTO>>>;
   public getAllResponsibilitesWithUsersRSQLUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<UserObjectResponsibilityMetaDataDTO>>>;
   public getAllResponsibilitesWithUsersRSQLUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

     if (search === null || search === undefined) {
         throw new Error(
             'Required parameter search was null or undefined when calling getAllResponsibilitesWithUsersRSQLUsingGET.'
         );
     }
     let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
     if (offset !== undefined && offset !== null) {
       queryParameters = queryParameters.set('offset', <any>offset);
     }
     if (search !== undefined && search !== null) {
         queryParameters = queryParameters.set('search', <any>search);
     }
     if (page !== undefined && page !== null) {
       queryParameters = queryParameters.set('page', <any>page);
     }
     if (pageNumber !== undefined && pageNumber !== null) {
       queryParameters = queryParameters.set('pageNumber', <any>pageNumber);
     }
     if (pageSize !== undefined && pageSize !== null) {
       queryParameters = queryParameters.set('pageSize', <any>pageSize);
     }
     if (paged !== undefined && paged !== null) {
       queryParameters = queryParameters.set('paged', <any>paged);
     }
     if (size !== undefined && size !== null) {
       queryParameters = queryParameters.set('size', <any>size);
     }
     if (sort) {
       sort.forEach((element) => {
         queryParameters = queryParameters.append('sort', <any>element);
       })
     }
     if (sortSorted !== undefined && sortSorted !== null) {
       queryParameters = queryParameters.set('sort.sorted', <any>sortSorted);
     }
     if (sortUnsorted !== undefined && sortUnsorted !== null) {
       queryParameters = queryParameters.set('sort.unsorted', <any>sortUnsorted);
     }
     if (unpaged !== undefined && unpaged !== null) {
       queryParameters = queryParameters.set('unpaged', <any>unpaged);
     }

     let headers = this.defaultHeaders;

     // to determine the Accept header
     let httpHeaderAccepts: string[] = [
       '*/*'
     ];
     const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
     if (httpHeaderAcceptSelected != undefined) {
       headers = headers.set('Accept', httpHeaderAcceptSelected);
     }

     // to determine the Content-Type header
     const consumes: string[] = [
     ];

     return this.httpClient.request<Array<UserObjectResponsibilityMetaDataDTO>>('get',`${this.basePath}/api/user-object-responsibility-meta-data-with-users-rsql`,
       {
         params: queryParameters,
         withCredentials: this.configuration.withCredentials,
         headers: headers,
         observe: observe,
         reportProgress: reportProgress
       }
     );
   }



    /**
         * getUserObjectResponsibilityMetaDataById
         *
         * @param id id
         * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
         * @param reportProgress flag to report request and response progress.
         */
    public getUserObjectResponsibilityMetaDataByIdUsingGET(
        id: number,
        observe?: 'body',
        reportProgress?: boolean
    ): Observable<UserObjectResponsibilityMetaDataDTO>;
    public getUserObjectResponsibilityMetaDataByIdUsingGET(
        id: number,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<UserObjectResponsibilityMetaDataDTO>>;
    public getUserObjectResponsibilityMetaDataByIdUsingGET(
        id: number,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<UserObjectResponsibilityMetaDataDTO>>;
    public getUserObjectResponsibilityMetaDataByIdUsingGET(
        id: number,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error(
                'Required parameter id was null or undefined when calling getUserObjectResponsibilityMetaDataByIdUsingGET.'
            );
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = ['*/*'];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [];

        return this.httpClient.request<UserObjectResponsibilityMetaDataDTO>(
            'get',
            `${this.basePath}/api/user-object-responsibility-meta-data/${encodeURIComponent(String(id))}`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress,
            }
        );
    }
 }
