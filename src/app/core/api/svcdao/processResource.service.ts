/**
 * Api Documentation
 * Api Documentation
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */ /* tslint:disable:no-unused-variable member-ordering */

import { HttpClient, HttpEvent, HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { Inject, Injectable, Optional } from '@angular/core';
import { CustomHttpUrlEncodingCodec } from '../encoder';

import { from, Observable } from 'rxjs';

import { ProcessDTO } from '@model/svcdao/processDTO';

import { FileInfoResponse } from '@model/svcdao/fileInfoResponse';
import { ProcessWithOperationPermissionsDTO } from '@model/svcdao/processWithOperationPermissionsDTO';
import { delay } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { Configuration } from '../configuration';
import { Constants } from '../constants';
import processesKPIQAData from '../staticData/10959.json';
import swotAnalysis from '../staticData/179.json';
import secoundryStratigy from '../staticData/76.json';
import projects from '../staticData/181.json';
import study from '../staticData/186.json';
import studyItms from '../staticData/188.json';
import businessProcessQAData from '../staticData/196.json';
import businessServiceData from '../staticData/4.json';
import businessProcessData from '../staticData/6.json';
import kpiData from '../staticData/95.json';
import { BASE_PATH } from '../variables';

@Injectable()
export class ProcessResourceService {
  protected basePath = Constants.SVCDAO_URL;
  public defaultHeaders = new HttpHeaders();
  public configuration = new Configuration();

  constructor(
    protected httpClient: HttpClient,
    @Optional() @Inject(BASE_PATH) basePath: string,
    @Optional() configuration: Configuration
  ) {
    if (basePath) {
      this.basePath = basePath;
    }
    if (configuration) {
      this.configuration = configuration;
      this.basePath = basePath ?? configuration.basePath ?? this.basePath;
    }
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    for (const consume of consumes) {
      if (form === consume) {
        return true;
      }
    }
    return false;
  }


  /**
   * getAllProcessRSQL
   *
   * @param search search
   * @param offset
   * @param page Page number of the requested page
   * @param pageNumber
   * @param pageSize
   * @param paged
   * @param size Size of a page
   * @param sort Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
   * @param sortSorted
   * @param sortUnsorted
   * @param unpaged
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getAllProcessRSQLUsingGET(
    search: string,
    page?: number,
    size?: number,
    sort?: Array<string>,
    observe?: 'body',
    reportProgress?: boolean
  ): Observable<Array<ProcessDTO>>;
  public getAllProcessRSQLUsingGET(
    search: string,
    page?: number,
    size?: number,
    sort?: Array<string>,
    observe?: 'response',
    reportProgress?: boolean
  ): Observable<HttpResponse<Array<ProcessDTO>>>;
  public getAllProcessRSQLUsingGET(
    search: string,
    page?: number,
    size?: number,
    sort?: Array<string>,
    observe?: 'events',
    reportProgress?: boolean
  ): Observable<HttpEvent<Array<ProcessDTO>>>;
  public getAllProcessRSQLUsingGET(
    search: string,
    page?: number,
    size?: number,
    sort?: Array<string>,
    observe: any = 'body',
    reportProgress: boolean = false
  ): Observable<any> {
    if (search === null || search === undefined) {
      throw new Error('Required parameter search was null or undefined when calling getAllProcessRSQLUsingGET.');
    }

    let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
    if (page !== undefined && page !== null) {
      queryParameters = queryParameters.set('page', <any> page);
    }
    if (search !== undefined && search !== null) {
      queryParameters = queryParameters.set('search', <any> search);
    }
    if (size !== undefined && size !== null) {
      queryParameters = queryParameters.set('size', <any> size);
    }
    if (sort) {
      sort.forEach((element) => {
        queryParameters = queryParameters.append('sort', <any> element);
      });
    }
    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = ['*/*'];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [];

    return this.httpClient.request<Array<ProcessDTO>>('get', `${this.basePath}/api/process-dao-rsql`, {
      params: queryParameters,
      withCredentials: this.configuration.withCredentials,
      headers: headers,
      observe: observe,
      reportProgress: reportProgress,
    });
  }

  /**
   * getAllProcesssTrimmedNoObjectType
   *
   * @param offset
   * @param page Page number of the requested page
   * @param pageNumber
   * @param pageSize
   * @param paged
   * @param size Size of a page
   * @param sort Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
   * @param sortSorted
   * @param sortUnsorted
   * @param unpaged
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getAllProcesssTrimmedNoObjectTypeUsingGET(
    offset?: number,
    page?: number,
    pageNumber?: number,
    pageSize?: number,
    paged?: boolean,
    size?: number,
    sort?: Array<string>,
    sortSorted?: boolean,
    sortUnsorted?: boolean,
    unpaged?: boolean,
    observe?: 'body',
    reportProgress?: boolean
  ): Observable<Array<ProcessDTO>>;
  public getAllProcesssTrimmedNoObjectTypeUsingGET(
    offset?: number,
    page?: number,
    pageNumber?: number,
    pageSize?: number,
    paged?: boolean,
    size?: number,
    sort?: Array<string>,
    sortSorted?: boolean,
    sortUnsorted?: boolean,
    unpaged?: boolean,
    observe?: 'response',
    reportProgress?: boolean
  ): Observable<HttpResponse<Array<ProcessDTO>>>;
  public getAllProcesssTrimmedNoObjectTypeUsingGET(
    offset?: number,
    page?: number,
    pageNumber?: number,
    pageSize?: number,
    paged?: boolean,
    size?: number,
    sort?: Array<string>,
    sortSorted?: boolean,
    sortUnsorted?: boolean,
    unpaged?: boolean,
    observe?: 'events',
    reportProgress?: boolean
  ): Observable<HttpEvent<Array<ProcessDTO>>>;
  public getAllProcesssTrimmedNoObjectTypeUsingGET(
    offset?: number,
    page?: number,
    pageNumber?: number,
    pageSize?: number,
    paged?: boolean,
    size?: number,
    sort?: Array<string>,
    sortSorted?: boolean,
    sortUnsorted?: boolean,
    unpaged?: boolean,
    observe: any = 'body',
    reportProgress: boolean = false
  ): Observable<any> {
    let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
    if (offset !== undefined && offset !== null) {
      queryParameters = queryParameters.set('offset', <any> offset);
    }
    if (page !== undefined && page !== null) {
      queryParameters = queryParameters.set('page', <any> page);
    }
    if (pageNumber !== undefined && pageNumber !== null) {
      queryParameters = queryParameters.set('pageNumber', <any> pageNumber);
    }
    if (pageSize !== undefined && pageSize !== null) {
      queryParameters = queryParameters.set('pageSize', <any> pageSize);
    }
    if (paged !== undefined && paged !== null) {
      queryParameters = queryParameters.set('paged', <any> paged);
    }
    if (size !== undefined && size !== null) {
      queryParameters = queryParameters.set('size', <any> size);
    }
    if (sort) {
      sort.forEach((element) => {
        queryParameters = queryParameters.append('sort', <any> element);
      });
    }
    if (sortSorted !== undefined && sortSorted !== null) {
      queryParameters = queryParameters.set('sort.sorted', <any> sortSorted);
    }
    if (sortUnsorted !== undefined && sortUnsorted !== null) {
      queryParameters = queryParameters.set('sort.unsorted', <any> sortUnsorted);
    }
    if (unpaged !== undefined && unpaged !== null) {
      queryParameters = queryParameters.set('unpaged', <any> unpaged);
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = ['*/*'];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [];

    return this.httpClient.request<Array<ProcessDTO>>(
      'get',
      `${this.basePath}/api/process-trimmed-no-object-type`,
      {
        params: queryParameters,
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress,
      }
    );
  }


  /**
   * getProcessById
   *
   * @param id id
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getProcessByIdUsingGET(id: number, observe?: 'body', reportProgress?: boolean): Observable<ProcessDTO>;
  public getProcessByIdUsingGET(
    id: number,
    observe?: 'response',
    reportProgress?: boolean
  ): Observable<HttpResponse<ProcessDTO>>;
  public getProcessByIdUsingGET(
    id: number,
    observe?: 'events',
    reportProgress?: boolean
  ): Observable<HttpEvent<ProcessDTO>>;
  public getProcessByIdUsingGET(id: number, observe: any = 'body', reportProgress: boolean = false): Observable<any> {
    if (id === null || id === undefined) {
      throw new Error('Required parameter id was null or undefined when calling getProcessByIdUsingGET.');
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = ['*/*'];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [];

    return this.httpClient.request<ProcessDTO>(
      'get',
      `${this.basePath}/api/process-dao/${encodeURIComponent(String(id))}`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress,
      }
    );
  }

  /**
   * updateProcess
   *
   * @param body processDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public updateProcessUsingPUT2(body: ProcessDTO, observe?: 'body', reportProgress?: boolean): Observable<ProcessDTO>;
  public updateProcessUsingPUT2(
    body: ProcessDTO,
    observe?: 'response',
    reportProgress?: boolean
  ): Observable<HttpResponse<ProcessDTO>>;
  public updateProcessUsingPUT2(
    body: ProcessDTO,
    observe?: 'events',
    reportProgress?: boolean
  ): Observable<HttpEvent<ProcessDTO>>;
  public updateProcessUsingPUT2(
    body: ProcessDTO,
    observe: any = 'body',
    reportProgress: boolean = false
  ): Observable<any> {
    if (body === null || body === undefined) {
      throw new Error('Required parameter body was null or undefined when calling updateProcessUsingPUT2.');
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = ['*/*'];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers = headers.set('Content-Type', httpContentTypeSelected);
    }

    return this.httpClient.request<ProcessDTO>('put', `${this.basePath}/api/process-dao`, {
      body: body,
      withCredentials: this.configuration.withCredentials,
      headers: headers,
      observe: observe,
      reportProgress: reportProgress,
    });
  }

  /**
   * uploadIcon
   *
   * @param file
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public uploadIconUsingPOST(file: Blob, observe?: 'body', reportProgress?: boolean): Observable<FileInfoResponse>;
  public uploadIconUsingPOST(
    file: Blob,
    observe?: 'response',
    reportProgress?: boolean
  ): Observable<HttpResponse<FileInfoResponse>>;
  public uploadIconUsingPOST(
    file: Blob,
    observe?: 'events',
    reportProgress?: boolean
  ): Observable<HttpEvent<FileInfoResponse>>;
  public uploadIconUsingPOST(file: Blob, observe: any = 'body', reportProgress: boolean = false): Observable<any> {
    if (file === null || file === undefined) {
      throw new Error('Required parameter file was null or undefined when calling uploadIconUsingPOST.');
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = ['*/*'];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = ['multipart/form-data'];

    const canConsumeForm = this.canConsumeForm(consumes);

    let formParams: { append(param: string, value: any): void };
    let useForm = false;
    let convertFormParamsToString = false;
    // use FormData to transmit files using content-type "multipart/form-data"
    // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
    useForm = canConsumeForm;
    if (useForm) {
      formParams = new FormData();
    } else {
      formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
    }

    if (file !== undefined) {
      formParams = (formParams.append('file', <any> file) as any) ?? formParams;
    }

    return this.httpClient.request<FileInfoResponse>('post', `${this.basePath}/api/process/uploadIcon`, {
      body: convertFormParamsToString ? JSON.stringify(formParams) : formParams,
      withCredentials: this.configuration.withCredentials,
      headers: headers,
      observe: observe,
      reportProgress: reportProgress,
    });
  }

  /**
   * getAllProcessByObjectMetaDataId
   *
   * @param id id
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getAllProcessByObjectMetaDataIdUsingGET(id: number, observe?: 'body', reportProgress?: boolean): Observable<ProcessWithOperationPermissionsDTO>;
  public getAllProcessByObjectMetaDataIdUsingGET(id: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<ProcessWithOperationPermissionsDTO>>;
  public getAllProcessByObjectMetaDataIdUsingGET(id: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<ProcessWithOperationPermissionsDTO>>;
  public getAllProcessByObjectMetaDataIdUsingGET(id: number, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (id === null || id === undefined) {
      throw new Error('Required parameter id was null or undefined when calling getAllProcessByObjectMetaDataIdUsingGET.');
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      '*/*'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [];

    if (!environment.production) {
      let data;
      if (id == 4) {
        //@ts-ignore
        data = businessServiceData as ProcessWithOperationPermissionsDTO;
      } else if (id == 6) {
        data = businessProcessData as ProcessWithOperationPermissionsDTO;
      }else if (id == 196) {
        //@ts-ignore
        data = businessProcessQAData as ProcessWithOperationPermissionsDTO;
      }else if (id == 10959) {
        data = processesKPIQAData as ProcessWithOperationPermissionsDTO;
      }
      if (id == 95) {
        data = kpiData as ProcessWithOperationPermissionsDTO;
      }
      if (id == 179) {
        data = swotAnalysis as ProcessWithOperationPermissionsDTO;
      }
      if (id == 181) {
        data = projects as ProcessWithOperationPermissionsDTO;
      }
      if (id == 186) {
        //@ts-ignore
        data = study as ProcessWithOperationPermissionsDTO;
      }
      if (id == 188) {
        data = studyItms as ProcessWithOperationPermissionsDTO;
      }
      if (id == 76) {
        //@ts-ignore
        data = secoundryStratigy as ProcessWithOperationPermissionsDTO;
      }
      return from([data]).pipe(delay(1000));
    }

    return this.httpClient.get<ProcessWithOperationPermissionsDTO>(`${this.basePath}/api/process-list-by-object-meta-data/${encodeURIComponent(String(id))}`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }
}
