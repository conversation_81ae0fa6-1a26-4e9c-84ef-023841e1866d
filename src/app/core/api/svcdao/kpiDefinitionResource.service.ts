/**
 * Api Documentation
 * Api Documentation
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */ /* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams, HttpResponse, HttpEvent } from '@angular/common/http';
import { CustomHttpUrlEncodingCodec } from '../encoder';

import { Observable } from 'rxjs';

import { BASE_PATH } from '../variables';
import { Configuration } from '../configuration';
import { Constants } from '../constants';

@Injectable()
export class KpiDefinitionResourceService {
    protected basePath = Constants.SVCDAO_URL;
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(
        protected httpClient: HttpClient,
        @Optional() @Inject(BASE_PATH) basePath: string,
        @Optional() configuration: Configuration
    ) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }


    /**
     * getAllKpiDefinitionsRSQLCount
     *
     * @param search search
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getAllKpiDefinitionsRSQLCountUsingGET(
        search: string,
        observe?: 'body',
        reportProgress?: boolean
    ): Observable<number>;
    public getAllKpiDefinitionsRSQLCountUsingGET(
        search: string,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<number>>;
    public getAllKpiDefinitionsRSQLCountUsingGET(
        search: string,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<number>>;
    public getAllKpiDefinitionsRSQLCountUsingGET(
        search: string,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (search === null || search === undefined) {
            throw new Error(
                'Required parameter search was null or undefined when calling getAllKpiDefinitionsRSQLCountUsingGET.'
            );
        }

        let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
        if (search !== undefined && search !== null) {
            queryParameters = queryParameters.set('search', <any>search);
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = ['*/*'];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [];

        return this.httpClient.request<number>('get', `${this.basePath}/api/kpi-definitions-rsql-count`, {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
            observe: observe,
            reportProgress: reportProgress,
        });
    }

}
