/**
 * Api Documentation
 * Api Documentation
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */ /* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams, HttpResponse, HttpEvent } from '@angular/common/http';
import { CustomHttpUrlEncodingCodec } from '../encoder';

import { Observable } from 'rxjs';

import { FrequencyDTO } from '@model/svcdao/frequencyDTO';

import { BASE_PATH } from '../variables';
import { Configuration } from '../configuration';
import { Constants } from '../constants';

@Injectable()
export class FrequencyResourceService {
    protected basePath = Constants.SVCDAO_URL;
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(
        protected httpClient: HttpClient,
        @Optional() @Inject(BASE_PATH) basePath: string,
        @Optional() configuration: Configuration
    ) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }


    /**
     * getAllFrequencyRSQL
     *
     * @param search search
     * @param offset
     * @param page Page number of the requested page
     * @param pageNumber
     * @param pageSize
     * @param paged
     * @param size Size of a page
     * @param sort Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @param sortSorted
     * @param sortUnsorted
     * @param unpaged
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getAllFrequencyRSQLUsingGET(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe?: 'body',
        reportProgress?: boolean
    ): Observable<Array<FrequencyDTO>>;
    public getAllFrequencyRSQLUsingGET(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<Array<FrequencyDTO>>>;
    public getAllFrequencyRSQLUsingGET(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<Array<FrequencyDTO>>>;
    public getAllFrequencyRSQLUsingGET(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (search === null || search === undefined) {
            throw new Error(
                'Required parameter search was null or undefined when calling getAllFrequencyRSQLUsingGET.'
            );
        }

        let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });

        if (page !== undefined && page !== null) {
            queryParameters = queryParameters.set('page', <any>page);
        }

        if (search !== undefined && search !== null) {
            queryParameters = queryParameters.set('search', <any>search);
        }
        if (size !== undefined && size !== null) {
            queryParameters = queryParameters.set('size', <any>size);
        }
        if (sort) {
            sort.forEach((element) => {
                queryParameters = queryParameters.append('sort', <any>element);
            });
        }
        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = ['*/*'];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [];

        return this.httpClient.request<Array<FrequencyDTO>>('get', `${this.basePath}/api/frequency-rsql`, {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
            observe: observe,
            reportProgress: reportProgress,
        });
    }

}
