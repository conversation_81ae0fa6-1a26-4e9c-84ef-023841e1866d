/**
 * Api Documentation
 * Api Documentation
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *//* tslint:disable:no-unused-variable member-ordering */

import { HttpClient, HttpEvent, HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { Inject, Injectable, Optional } from '@angular/core';
import { DictionaryDTO } from '@model/svcdao/dictionaryDTO';
import { Observable } from 'rxjs';
import { Configuration } from '../configuration';
import { Constants } from '../constants';
import { CustomHttpUrlEncodingCodec } from '../encoder';
import { BASE_PATH } from '../variables';


@Injectable()
export class DictionaryResourceService {

    protected basePath = Constants.SVCDAO_URL;
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(protected httpClient: HttpClient, @Optional()@Inject(BASE_PATH) basePath: string, @Optional() configuration: Configuration) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }
    /**
     * createDictionary
     *
     * @param body dictionaryDTO
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createDictionaryUsingPOST(body: DictionaryDTO, observe?: 'body', reportProgress?: boolean): Observable<DictionaryDTO>;
    public createDictionaryUsingPOST(body: DictionaryDTO, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<DictionaryDTO>>;
    public createDictionaryUsingPOST(body: DictionaryDTO, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<DictionaryDTO>>;
    public createDictionaryUsingPOST(body: DictionaryDTO, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (body === null || body === undefined) {
            throw new Error('Required parameter body was null or undefined when calling createDictionaryUsingPOST.');
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            '*/*'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.request<DictionaryDTO>('post',`${this.basePath}/api/dictionaries`,
            {
                body: body,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * deleteDictionary
     *
     * @param id id
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteDictionaryUsingDELETE(id: number, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public deleteDictionaryUsingDELETE(id: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public deleteDictionaryUsingDELETE(id: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public deleteDictionaryUsingDELETE(id: number, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling deleteDictionaryUsingDELETE.');
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.request<any>('delete',`${this.basePath}/api/dictionaries/${encodeURIComponent(String(id))}`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }


    /**
     * exportDictionary
     *
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public exportDictionaryUsingGET(observe?: 'body', reportProgress?: boolean): Observable<string>;
    public exportDictionaryUsingGET(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<string>>;
    public exportDictionaryUsingGET(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<string>>;
    public exportDictionaryUsingGET(observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            '*/*'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.request('get',`${this.basePath}/api/dictionary-export`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress,
                responseType: 'blob'
            }
        );
    }

    /**
     * exportEmptyDictionaryFile
     *
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public exportEmptyDictionaryFileUsingGET(observe?: 'body', reportProgress?: boolean): Observable<string>;
    public exportEmptyDictionaryFileUsingGET(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<string>>;
    public exportEmptyDictionaryFileUsingGET(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<string>>;
    public exportEmptyDictionaryFileUsingGET(observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            '*/*'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.request('get',`${this.basePath}/api/dictionary-export-empty`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress,
                responseType: 'blob'
            }
        );
    }

    /**
     * getAllDictionaryRSQLCount
     *
     * @param search search
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getAllDictionaryRSQLCountUsingGET(search: string, observe?: 'body', reportProgress?: boolean): Observable<number>;
    public getAllDictionaryRSQLCountUsingGET(search: string, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<number>>;
    public getAllDictionaryRSQLCountUsingGET(search: string, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<number>>;
    public getAllDictionaryRSQLCountUsingGET(search: string, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (search === null || search === undefined) {
            throw new Error('Required parameter search was null or undefined when calling getAllDictionaryRSQLCountUsingGET.');
        }

        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (search !== undefined && search !== null) {
            queryParameters = queryParameters.set('search', <any>search);
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            '*/*'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.request<number>('get',`${this.basePath}/api/dictionaries-rsql-count`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }


    /**
     * getAllDictionaryRSQLWithTranlsations
     *
     * @param search search
     * @param offset
     * @param page Page number of the requested page
     * @param pageNumber
     * @param pageSize
     * @param paged
     * @param size Size of a page
     * @param sort Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @param sortSorted
     * @param sortUnsorted
     * @param unpaged
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getAllDictionaryRSQLWithTranlsationsUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'body', reportProgress?: boolean): Observable<Array<DictionaryDTO>>;
    public getAllDictionaryRSQLWithTranlsationsUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<DictionaryDTO>>>;
    public getAllDictionaryRSQLWithTranlsationsUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<DictionaryDTO>>>;
    public getAllDictionaryRSQLWithTranlsationsUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (search === null || search === undefined) {
            throw new Error('Required parameter search was null or undefined when calling getAllDictionaryRSQLWithTranlsationsUsingGET.');
        }
        let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
        if (offset !== undefined && offset !== null) {
            queryParameters = queryParameters.set('offset', <any>offset);
        }
        if (page !== undefined && page !== null) {
            queryParameters = queryParameters.set('page', <any>page);
        }
        if (pageNumber !== undefined && pageNumber !== null) {
            queryParameters = queryParameters.set('pageNumber', <any>pageNumber);
        }
        if (pageSize !== undefined && pageSize !== null) {
            queryParameters = queryParameters.set('pageSize', <any>pageSize);
        }
        if (paged !== undefined && paged !== null) {
            queryParameters = queryParameters.set('paged', <any>paged);
        }
        if (search !== undefined && search !== null) {
            queryParameters = queryParameters.set('search', <any>search);
        }
        if (size !== undefined && size !== null) {
            queryParameters = queryParameters.set('size', <any>size);
        }
        if (sort) {
            sort.forEach((element) => {
                queryParameters = queryParameters.append('sort', <any>element);
            })
        }
        if (sortSorted !== undefined && sortSorted !== null) {
            queryParameters = queryParameters.set('sort.sorted', <any>sortSorted);
        }
        if (sortUnsorted !== undefined && sortUnsorted !== null) {
            queryParameters = queryParameters.set('sort.unsorted', <any>sortUnsorted);
        }
        if (unpaged !== undefined && unpaged !== null) {
            queryParameters = queryParameters.set('unpaged', <any>unpaged);
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            '*/*'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.request<Array<DictionaryDTO>>('get',`${this.basePath}/api/dictionaries-rsql-with-translation`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * getAllDictionaryWithTranlsations
     *
     * @param languageId languageId
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getAllDictionaryWithTranlsationsUsingGET(languageId: number, observe?: 'body', reportProgress?: boolean): Observable<Array<DictionaryDTO>>;
    public getAllDictionaryWithTranlsationsUsingGET(languageId: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<DictionaryDTO>>>;
    public getAllDictionaryWithTranlsationsUsingGET(languageId: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<DictionaryDTO>>>;
    public getAllDictionaryWithTranlsationsUsingGET(languageId: number, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (languageId === null || languageId === undefined) {
            throw new Error('Required parameter languageId was null or undefined when calling getAllDictionaryWithTranlsationsUsingGET.');
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            '*/*'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.request<Array<DictionaryDTO>>('get',`${this.basePath}/api/dictionaries-with-translation/${encodeURIComponent(String(languageId))}`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * getDictionaryById
     *
     * @param id id
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getDictionaryByIdUsingGET(id: number, observe?: 'body', reportProgress?: boolean): Observable<DictionaryDTO>;
    public getDictionaryByIdUsingGET(id: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<DictionaryDTO>>;
    public getDictionaryByIdUsingGET(id: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<DictionaryDTO>>;
    public getDictionaryByIdUsingGET(id: number, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling getDictionaryByIdUsingGET.');
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            '*/*'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.request<DictionaryDTO>('get',`${this.basePath}/api/dictionaries/${encodeURIComponent(String(id))}`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * restoreBackup
     *
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public restoreBackupUsingGET(observe?: 'body', reportProgress?: boolean): Observable<any>;
    public restoreBackupUsingGET(observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
    public restoreBackupUsingGET(observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
    public restoreBackupUsingGET(observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
        ];

        return this.httpClient.request<any>('get',`${this.basePath}/api/restore-backup`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * updateDictionary
     *
     * @param body dictionaryDTO
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateDictionaryUsingPUT(body: DictionaryDTO, observe?: 'body', reportProgress?: boolean): Observable<DictionaryDTO>;
    public updateDictionaryUsingPUT(body: DictionaryDTO, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<DictionaryDTO>>;
    public updateDictionaryUsingPUT(body: DictionaryDTO, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<DictionaryDTO>>;
    public updateDictionaryUsingPUT(body: DictionaryDTO, observe: any = 'body', reportProgress: boolean = false ): Observable<any> {

        if (body === null || body === undefined) {
            throw new Error('Required parameter body was null or undefined when calling updateDictionaryUsingPUT.');
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [
            '*/*'
        ];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.request<DictionaryDTO>('put',`${this.basePath}/api/dictionaries`,
            {
                body: body,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress
            }
        );
    }

}
