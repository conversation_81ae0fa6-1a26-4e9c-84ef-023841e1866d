/**
 * Api Documentation
 * Api Documentation
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */ /* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams, HttpResponse, HttpEvent } from '@angular/common/http';
import { CustomHttpUrlEncodingCodec } from '../encoder';

import { Observable } from 'rxjs';

import { SvgIconDTO } from '@model/svcdao/svgIconDTO';

import { BASE_PATH } from '../variables';
import { Configuration } from '../configuration';
import { Constants } from '../constants';

@Injectable()
export class SvgIconResourceService {
    protected basePath = Constants.SVCDAO_URL;
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(
        protected httpClient: HttpClient,
        @Optional() @Inject(BASE_PATH) basePath: string,
        @Optional() configuration: Configuration
    ) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }

    /**
     * createSvgIcon
     *
     * @param body svgIconDTO
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createSvgIconUsingPOST(body: SvgIconDTO, observe?: 'body', reportProgress?: boolean): Observable<SvgIconDTO>;
    public createSvgIconUsingPOST(
        body: SvgIconDTO,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<SvgIconDTO>>;
    public createSvgIconUsingPOST(
        body: SvgIconDTO,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<SvgIconDTO>>;
    public createSvgIconUsingPOST(
        body: SvgIconDTO,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (body === null || body === undefined) {
            throw new Error('Required parameter body was null or undefined when calling createSvgIconUsingPOST.');
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = ['*/*'];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = ['application/json'];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.request<SvgIconDTO>('post', `${this.basePath}/api/svg-icons`, {
            body: body,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
            observe: observe,
            reportProgress: reportProgress,
        });
    }

    /**
     * deleteSvgIcon
     *
     * @param id id
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteSvgIconUsingDELETE(id: number, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public deleteSvgIconUsingDELETE(
        id: number,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<any>>;
    public deleteSvgIconUsingDELETE(
        id: number,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<any>>;
    public deleteSvgIconUsingDELETE(
        id: number,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling deleteSvgIconUsingDELETE.');
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [];

        return this.httpClient.request<any>(
            'delete',
            `${this.basePath}/api/svg-icons/${encodeURIComponent(String(id))}`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress,
            }
        );
    }


    /**
     * getAllDataRSQL
     *
     * @param search search
     * @param offset
     * @param page Page number of the requested page
     * @param pageNumber
     * @param pageSize
     * @param paged
     * @param size Size of a page
     * @param sort Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @param sortSorted
     * @param sortUnsorted
     * @param unpaged
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getAllDataRSQLUsingGET16(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe?: 'body',
        reportProgress?: boolean
    ): Observable<Array<SvgIconDTO>>;
    public getAllDataRSQLUsingGET16(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<Array<SvgIconDTO>>>;
    public getAllDataRSQLUsingGET16(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<Array<SvgIconDTO>>>;
    public getAllDataRSQLUsingGET16(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (search === null || search === undefined) {
            throw new Error(
                'Required parameter search was null or undefined when calling getAllRepositoryDBRSQLUsingGET.'
            );
        }
        let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });

        if (page !== undefined && page !== null) {
            queryParameters = queryParameters.set('page', <any>page);
        }

        if (search !== undefined && search !== null) {
            queryParameters = queryParameters.set('search', <any>search);
        }
        if (size !== undefined && size !== null) {
            queryParameters = queryParameters.set('size', <any>size);
        }
        if (sort) {
            sort.forEach((element) => {
                queryParameters = queryParameters.append('sort', <any>element);
            });
        }
        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = ['*/*'];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [];

        return this.httpClient.request<Array<SvgIconDTO>>('get', `${this.basePath}/api/svg-icons-rsql`, {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
            observe: observe,
            reportProgress: reportProgress,
        });
    }

}
