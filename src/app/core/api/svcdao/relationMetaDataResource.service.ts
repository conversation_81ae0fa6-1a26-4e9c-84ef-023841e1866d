/**
 * Api Documentation
 * Api Documentation
 *
 * OpenAPI spec version: 1.0
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */ /* tslint:disable:no-unused-variable member-ordering */

import { HttpClient, HttpEvent, HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { Inject, Injectable, Optional } from '@angular/core';
import { CustomHttpUrlEncodingCodec } from '../encoder';

import { Observable } from 'rxjs';

import { RelationMetaDataDTO } from '@model/svcdao/relationMetaDataDTO';

import { Configuration } from '../configuration';
import { Constants } from '../constants';
import { BASE_PATH } from '../variables';

@Injectable()
export class RelationMetaDataResourceService {
    protected basePath = Constants.SVCDAO_URL;
    public defaultHeaders = new HttpHeaders();
    public configuration = new Configuration();

    constructor(
        protected httpClient: HttpClient,
        @Optional() @Inject(BASE_PATH) basePath: string,
        @Optional() configuration: Configuration
    ) {
        if (basePath) {
            this.basePath = basePath;
        }
        if (configuration) {
            this.configuration = configuration;
            this.basePath = basePath || configuration.basePath || this.basePath;
        }
    }

    /**
     * createRelationMetaData
     *
     * @param body relationMetaDataDTO
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public createRelationMetaDataUsingPOST(
        body: RelationMetaDataDTO,
        observe?: 'body',
        reportProgress?: boolean
    ): Observable<RelationMetaDataDTO>;
    public createRelationMetaDataUsingPOST(
        body: RelationMetaDataDTO,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<RelationMetaDataDTO>>;
    public createRelationMetaDataUsingPOST(
        body: RelationMetaDataDTO,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<RelationMetaDataDTO>>;
    public createRelationMetaDataUsingPOST(
        body: RelationMetaDataDTO,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (body === null || body === undefined) {
            throw new Error(
                'Required parameter body was null or undefined when calling createRelationMetaDataUsingPOST.'
            );
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = ['*/*'];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = ['application/json'];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.request<RelationMetaDataDTO>('post', `${this.basePath}/api/relation-meta-data`, {
            body: body,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
            observe: observe,
            reportProgress: reportProgress,
        });
    }

    /**
     * deleteRelationMetaData
     *
     * @param id id
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deleteRelationMetaDataUsingDELETE(id: number, observe?: 'body', reportProgress?: boolean): Observable<any>;
    public deleteRelationMetaDataUsingDELETE(
        id: number,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<any>>;
    public deleteRelationMetaDataUsingDELETE(
        id: number,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<any>>;
    public deleteRelationMetaDataUsingDELETE(
        id: number,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error(
                'Required parameter id was null or undefined when calling deleteRelationMetaDataUsingDELETE.'
            );
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = [];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [];

        return this.httpClient.request<any>(
            'delete',
            `${this.basePath}/api/relation-meta-data/${encodeURIComponent(String(id))}`,
            {
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress,
            }
        );
    }

   /**
     * getAllRelationMetaDataRSQLCount
     *
     * @param search search
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getAllRelationMetaDataRSQLCountUsingGET(
        search: string,
        observe?: 'body',
        reportProgress?: boolean
    ): Observable<number>;
    public getAllRelationMetaDataRSQLCountUsingGET(
        search: string,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<number>>;
    public getAllRelationMetaDataRSQLCountUsingGET(
        search: string,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<number>>;
    public getAllRelationMetaDataRSQLCountUsingGET(
        search: string,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (search === null || search === undefined) {
            throw new Error(
                'Required parameter search was null or undefined when calling getAllRelationMetaDataRSQLCountUsingGET.'
            );
        }

        let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });
        if (search !== undefined && search !== null) {
            queryParameters = queryParameters.set('search', <any>search);
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = ['*/*'];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [];

        return this.httpClient.request<number>('get', `${this.basePath}/api/relation-meta-data-rsql-count`, {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
            observe: observe,
            reportProgress: reportProgress,
        });
    }

    /**
     * getAllRelationMetaDataRSQL
     *
     * @param search search
     * @param offset
     * @param page Page number of the requested page
     * @param pageNumber
     * @param pageSize
     * @param paged
     * @param size Size of a page
     * @param sort Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @param sortSorted
     * @param sortUnsorted
     * @param unpaged
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public getAllRelationMetaDataRSQLUsingGET(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe?: 'body',
        reportProgress?: boolean
    ): Observable<Array<RelationMetaDataDTO>>;
    public getAllRelationMetaDataRSQLUsingGET(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<Array<RelationMetaDataDTO>>>;
    public getAllRelationMetaDataRSQLUsingGET(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<Array<RelationMetaDataDTO>>>;
    public getAllRelationMetaDataRSQLUsingGET(
        search: string,
        page?: number,
        size?: number,
        sort?: Array<string>,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (search === null || search === undefined) {
            throw new Error(
                'Required parameter search was null or undefined when calling getAllRelationMetaDataRSQLUsingGET.'
            );
        }

        let queryParameters = new HttpParams({ encoder: new CustomHttpUrlEncodingCodec() });

        if (page !== undefined && page !== null) {
            queryParameters = queryParameters.set('page', <any>page);
        }

        if (search !== undefined && search !== null) {
            queryParameters = queryParameters.set('search', <any>search);
        }
        if (size !== undefined && size !== null) {
            queryParameters = queryParameters.set('size', <any>size);
        }
        if (sort) {
            sort.forEach((element) => {
                queryParameters = queryParameters.append('sort', <any>element);
            });
        }
        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = ['*/*'];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = [];

        return this.httpClient.request<Array<RelationMetaDataDTO>>(
            'get',
            `${this.basePath}/api/relation-meta-data-rsql-trimmed`,
            {
                params: queryParameters,
                withCredentials: this.configuration.withCredentials,
                headers: headers,
                observe: observe,
                reportProgress: reportProgress,
            }
        );
    }


    /**
     * updateRelationMetaData
     *
     * @param body relationMetaDataDTO
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public updateRelationMetaDataUsingPUT(
        body: RelationMetaDataDTO,
        observe?: 'body',
        reportProgress?: boolean
    ): Observable<RelationMetaDataDTO>;
    public updateRelationMetaDataUsingPUT(
        body: RelationMetaDataDTO,
        observe?: 'response',
        reportProgress?: boolean
    ): Observable<HttpResponse<RelationMetaDataDTO>>;
    public updateRelationMetaDataUsingPUT(
        body: RelationMetaDataDTO,
        observe?: 'events',
        reportProgress?: boolean
    ): Observable<HttpEvent<RelationMetaDataDTO>>;
    public updateRelationMetaDataUsingPUT(
        body: RelationMetaDataDTO,
        observe: any = 'body',
        reportProgress: boolean = false
    ): Observable<any> {
        if (body === null || body === undefined) {
            throw new Error(
                'Required parameter body was null or undefined when calling updateRelationMetaDataUsingPUT.'
            );
        }

        let headers = this.defaultHeaders;

        // to determine the Accept header
        let httpHeaderAccepts: string[] = ['*/*'];
        const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
        if (httpHeaderAcceptSelected != undefined) {
            headers = headers.set('Accept', httpHeaderAcceptSelected);
        }

        // to determine the Content-Type header
        const consumes: string[] = ['application/json'];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected != undefined) {
            headers = headers.set('Content-Type', httpContentTypeSelected);
        }

        return this.httpClient.request<RelationMetaDataDTO>('put', `${this.basePath}/api/relation-meta-data`, {
            body: body,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
            observe: observe,
            reportProgress: reportProgress,
        });
    }

    }
