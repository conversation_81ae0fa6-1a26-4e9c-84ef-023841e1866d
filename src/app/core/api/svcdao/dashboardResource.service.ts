/**
 * svcbusinessdash API
 * svcbusinessdash API documentation
 *
 * OpenAPI spec version: 0.0.1
 *
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */ /* tslint:disable:no-unused-variable member-ordering */

import { HttpClient, HttpEvent, HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { Inject, Injectable, Optional } from '@angular/core';
import { CustomHttpUrlEncodingCodec } from '../encoder';
import { Observable } from 'rxjs';
import { DashboardEntityDTO } from "@model/svcdao/dashboardEntityDTO";
import { Configuration } from '../configuration';
import { Constants } from '../constants';
import { BASE_PATH } from '../variables';

@Injectable()
export class DashboardResourceService {
  protected basePath = Constants.SVCDAO_URL;
  public defaultHeaders = new HttpHeaders();
  public configuration = new Configuration();

  constructor(
    protected httpClient: HttpClient,
    @Optional() @Inject(BASE_PATH) basePath: string,
    @Optional() configuration: Configuration
  ) {
    if (basePath) {
      this.basePath = basePath;
    }
    if (configuration) {
      this.configuration = configuration;
      this.basePath = basePath || configuration.basePath || this.basePath;
    }
  }
  /**
   * getAllDashboardEntityRSQL
   *
   * @param search search
   * @param offset
   * @param page Page number of the requested page
   * @param pageNumber
   * @param pageSize
   * @param paged
   * @param size Size of a page
   * @param sort Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
   * @param sortSorted
   * @param sortUnsorted
   * @param unpaged
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getAllDashboardEntityRSQLUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'body', reportProgress?: boolean): Observable<Array<DashboardEntityDTO>>;
  public getAllDashboardEntityRSQLUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<Array<DashboardEntityDTO>>>;
  public getAllDashboardEntityRSQLUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<Array<DashboardEntityDTO>>>;
  public getAllDashboardEntityRSQLUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (search === null || search === undefined) {
      throw new Error('Required parameter search was null or undefined when calling getAllDashboardEntityRSQLUsingGET.');
    }

    let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
    if (offset !== undefined && offset !== null) {
      queryParameters = queryParameters.set('offset', <any>offset);
    }
    if (page !== undefined && page !== null) {
      queryParameters = queryParameters.set('page', <any>page);
    }
    if (pageNumber !== undefined && pageNumber !== null) {
      queryParameters = queryParameters.set('pageNumber', <any>pageNumber);
    }
    if (pageSize !== undefined && pageSize !== null) {
      queryParameters = queryParameters.set('pageSize', <any>pageSize);
    }
    if (paged !== undefined && paged !== null) {
      queryParameters = queryParameters.set('paged', <any>paged);
    }
    if (search !== undefined && search !== null) {
      queryParameters = queryParameters.set('search', <any>search);
    }
    if (size !== undefined && size !== null) {
      queryParameters = queryParameters.set('size', <any>size);
    }
    if (sort) {
      sort.forEach((element) => {
        queryParameters = queryParameters.append('sort', <any>element);
      })
    }
    if (sortSorted !== undefined && sortSorted !== null) {
      queryParameters = queryParameters.set('sort.sorted', <any>sortSorted);
    }
    if (sortUnsorted !== undefined && sortUnsorted !== null) {
      queryParameters = queryParameters.set('sort.unsorted', <any>sortUnsorted);
    }
    if (unpaged !== undefined && unpaged !== null) {
      queryParameters = queryParameters.set('unpaged', <any>unpaged);
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      '*/*'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    return this.httpClient.request<Array<DashboardEntityDTO>>('get', `${this.basePath}/api/dashboard-entity-rsql`,
      {
        params: queryParameters,
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }





  /**
   * getAllDashboardEntityRSQLCount
   *
   * @param search search
   * @param offset
   * @param page Page number of the requested page
   * @param pageNumber
   * @param pageSize
   * @param paged
   * @param size Size of a page
   * @param sort Sorting criteria in the format: property(,asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
   * @param sortSorted
   * @param sortUnsorted
   * @param unpaged
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getAllDashboardEntityRSQLCountUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'body', reportProgress?: boolean): Observable<number>;
  public getAllDashboardEntityRSQLCountUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<number>>;
  public getAllDashboardEntityRSQLCountUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<number>>;
  public getAllDashboardEntityRSQLCountUsingGET(search: string, offset?: number, page?: number, pageNumber?: number, pageSize?: number, paged?: boolean, size?: number, sort?: Array<string>, sortSorted?: boolean, sortUnsorted?: boolean, unpaged?: boolean, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (search === null || search === undefined) {
      throw new Error('Required parameter search was null or undefined when calling getAllDashboardEntityRSQLUsingGET.');
    }

    let queryParameters = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
    if (offset !== undefined && offset !== null) {
      queryParameters = queryParameters.set('offset', <any>offset);
    }
    if (page !== undefined && page !== null) {
      queryParameters = queryParameters.set('page', <any>page);
    }
    if (pageNumber !== undefined && pageNumber !== null) {
      queryParameters = queryParameters.set('pageNumber', <any>pageNumber);
    }
    if (pageSize !== undefined && pageSize !== null) {
      queryParameters = queryParameters.set('pageSize', <any>pageSize);
    }
    if (paged !== undefined && paged !== null) {
      queryParameters = queryParameters.set('paged', <any>paged);
    }
    if (search !== undefined && search !== null) {
      queryParameters = queryParameters.set('search', <any>search);
    }
    if (size !== undefined && size !== null) {
      queryParameters = queryParameters.set('size', <any>size);
    }
    if (sort) {
      sort.forEach((element) => {
        queryParameters = queryParameters.append('sort', <any>element);
      })
    }
    if (sortSorted !== undefined && sortSorted !== null) {
      queryParameters = queryParameters.set('sort.sorted', <any>sortSorted);
    }
    if (sortUnsorted !== undefined && sortUnsorted !== null) {
      queryParameters = queryParameters.set('sort.unsorted', <any>sortUnsorted);
    }
    if (unpaged !== undefined && unpaged !== null) {
      queryParameters = queryParameters.set('unpaged', <any>unpaged);
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      '*/*'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    return this.httpClient.request<Array<DashboardEntityDTO>>('get', `${this.basePath}/api/dashboard-entity-rsql-count`,
      {
        params: queryParameters,
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * createDashboardEntity
   *
   * @param body dashboardDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public createDashboardEntityUsingPOST(body: DashboardEntityDTO, observe?: 'body', reportProgress?: boolean): Observable<DashboardEntityDTO>;
  public createDashboardEntityUsingPOST(body: DashboardEntityDTO, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<DashboardEntityDTO>>;
  public createDashboardEntityUsingPOST(body: DashboardEntityDTO, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<DashboardEntityDTO>>;
  public createDashboardEntityUsingPOST(body: DashboardEntityDTO, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (body === null || body === undefined) {
      throw new Error('Required parameter body was null or undefined when calling createDashboardEntityUsingPOST.');
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      '*/*'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/json'
    ];
    const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers = headers.set('Content-Type', httpContentTypeSelected);
    }

    return this.httpClient.request<DashboardEntityDTO>('post', `${this.basePath}/api/dashboard-entity`,
      {
        body: body,
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * getDashboardEntity
   *
   * @param id id
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getDashboardEntityUsingGET(id: number, observe?: 'body', reportProgress?: boolean): Observable<DashboardEntityDTO>;
  public getDashboardEntityUsingGET(id: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<DashboardEntityDTO>>;
  public getDashboardEntityUsingGET(id: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<DashboardEntityDTO>>;
  public getDashboardEntityUsingGET(id: number, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (id === null || id === undefined) {
      throw new Error('Required parameter id was null or undefined when calling getDashboardEntityUsingGET.');
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      '*/*'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    return this.httpClient.request<DashboardEntityDTO>('get', `${this.basePath}/api/dashboard-entity/${encodeURIComponent(String(id))}`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * updateDashboardEntity
   *
   * @param body dashboardEntityDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public updateDashboardEntityUsingPUT(body: DashboardEntityDTO, observe?: 'body', reportProgress?: boolean): Observable<DashboardEntityDTO>;
  public updateDashboardEntityUsingPUT(body: DashboardEntityDTO, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<DashboardEntityDTO>>;
  public updateDashboardEntityUsingPUT(body: DashboardEntityDTO, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<DashboardEntityDTO>>;
  public updateDashboardEntityUsingPUT(body: DashboardEntityDTO, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (body === null || body === undefined) {
      throw new Error('Required parameter body was null or undefined when calling updateDashboardEntityUsingPUT.');
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [
      '*/*'
    ];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    // to determine the Content-Type header
    const consumes: string[] = [
      'application/json'
    ];
    const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers = headers.set('Content-Type', httpContentTypeSelected);
    }

    return this.httpClient.request<DashboardEntityDTO>('put', `${this.basePath}/api/dashboard-entity`,
      {
        body: body,
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }

  /**
   * deleteDashboardEntity
   *
   * @param id id
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public deleteDashboardEntityUsingDELETE(id: number, observe?: 'body', reportProgress?: boolean): Observable<any>;
  public deleteDashboardEntityUsingDELETE(id: number, observe?: 'response', reportProgress?: boolean): Observable<HttpResponse<any>>;
  public deleteDashboardEntityUsingDELETE(id: number, observe?: 'events', reportProgress?: boolean): Observable<HttpEvent<any>>;
  public deleteDashboardEntityUsingDELETE(id: number, observe: any = 'body', reportProgress: boolean = false): Observable<any> {

    if (id === null || id === undefined) {
      throw new Error('Required parameter id was null or undefined when calling deleteDashboardEntityUsingDELETE.');
    }

    let headers = this.defaultHeaders;

    // to determine the Accept header
    let httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined = this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers = headers.set('Accept', httpHeaderAcceptSelected);
    }

    return this.httpClient.request<any>('delete', `${this.basePath}/api/dashboard-entity/${encodeURIComponent(String(id))}`,
      {
        withCredentials: this.configuration.withCredentials,
        headers: headers,
        observe: observe,
        reportProgress: reportProgress
      }
    );
  }


}

