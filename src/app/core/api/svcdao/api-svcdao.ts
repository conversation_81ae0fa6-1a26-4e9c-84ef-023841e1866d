import {ResponsibilityQueryResourceService} from './responsibilityQueryResource.service';
export * from './attributeMetaDataResource.service';
import { AttributeMetaDataResourceService } from './attributeMetaDataResource.service';
export * from './frequencyResource.service';
import { FrequencyResourceService } from './frequencyResource.service';
export * from './hierarchyMetaDataResource.service';
import { HierarchyMetaDataResourceService } from './hierarchyMetaDataResource.service';
export * from './kpiDefinitionResource.service';
import { KpiDefinitionResourceService } from './kpiDefinitionResource.service';
export * from './languageResource.service';
import { LanguageResourceService } from './languageResource.service';
export * from './metricDefinitionResource.service';
import { MetricDefinitionResourceService } from './metricDefinitionResource.service';
export * from './objectInstanceResource.service';
import { ObjectInstanceResourceService } from './objectInstanceResource.service';
export * from './objectMetaDataResource.service';
import { ObjectMetaDataResourceService } from './objectMetaDataResource.service';
export * from './objectMetaDataStateResource.service';
import { ObjectMetaDataStateResourceService } from './objectMetaDataStateResource.service';
export * from './relationMetaDataResource.service';
import { RelationMetaDataResourceService } from './relationMetaDataResource.service';
export * from './repositoryDbResource.service';
import { RepositoryDbResourceService } from './repositoryDbResource.service';
export * from './svgIconResource.service';
import { SvgIconResourceService } from './svgIconResource.service';
export * from './userObjectResponsibilityMetaDataResource.service';
import { UserObjectResponsibilityMetaDataResourceService } from './userObjectResponsibilityMetaDataResource.service';
export * from './userResource.service';
import { UserResourceService } from './userResource.service';
export * from './hierarchyKpiResource.service';
import { HierarchyKpiResourceService } from './hierarchyKpiResource.service';
export * from './hierarchyMetricResource.service';
import { HierarchyMetricResourceService } from './hierarchyMetricResource.service';
export * from './formCategoryResource.service';
import { FormCategoryResourceService } from './formCategoryResource.service';
export * from './formTemplateResource.service';
import { FormTemplateResourceService } from './formTemplateResource.service';
export * from './thresholdCategoryResource.service';
import { ThresholdCategoryResourceService } from './thresholdCategoryResource.service';
export * from './thresholdResource.service';
import { ThresholdResourceService } from './thresholdResource.service';
export * from './objectMetaDataDiagramResource.service';
import { ObjectMetaDataDiagramResourceService } from './objectMetaDataDiagramResource.service';
import { ResponsibilityUserResourceService } from './responsibilityUserResource.service';
import { ResponsibilityObjectMetaDataResourceService } from './responsibilityObjectMetaDataResource.service';
import { DictionaryResourceService } from './dictionaryResource.service';
import { TranslationResourceService } from './translationResource.service';
export * from '@services/tranProcessResource.service';
import { TranProcessResourceService } from '@services/tranProcessResource.service';
export * from './processBusinResource.service';
import { ProcessBusinResourceService } from './processBusinResource.service';
export * from './processResource.service';
import { ProcessResourceService } from './processResource.service';
export * from './licenseManagementResource.service';
import { LicenseManagementResourceService } from './licenseManagementResource.service';
export * from './systemLogoResource.service';
import { SystemLogoResourceService } from './systemLogoResource.service';
export * from './dashboardResource.service';
import { DashboardResourceService } from './dashboardResource.service';
import { GovernanceResourceService } from './governanceResource.service';
import { WorkflowResourceService } from './workflow-resource.service';
import { DashboardCategoryResourceService } from './dashboardCategoryResource.service';
export * from './dashboardCategoryResource.service';
import { LicenseResourceService } from './licenseResource.service';
import { UserGroupResourceService } from './userGroupResource.service';
import { ObjectInstanceFilterControllerService } from './objectInstanceFilterController.service';
export * from './licenseResource.service';

export const APIS_SVCDAO = [
    AttributeMetaDataResourceService,
    FrequencyResourceService,
    HierarchyMetaDataResourceService,
    KpiDefinitionResourceService,
    LanguageResourceService,
    MetricDefinitionResourceService,
    ObjectInstanceResourceService,
    ObjectMetaDataResourceService,
    ObjectMetaDataStateResourceService,
    RelationMetaDataResourceService,
    RepositoryDbResourceService,
    SvgIconResourceService,
    UserObjectResponsibilityMetaDataResourceService,
    UserResourceService,
    HierarchyKpiResourceService,
    HierarchyMetricResourceService,
    FormCategoryResourceService,
    FormTemplateResourceService,
    ThresholdCategoryResourceService,
    ThresholdResourceService,
    ObjectMetaDataDiagramResourceService,
    ResponsibilityUserResourceService,
    ResponsibilityObjectMetaDataResourceService,
    DictionaryResourceService,
    TranslationResourceService,
    TranProcessResourceService,
    ProcessBusinResourceService,
    ProcessResourceService,
    LicenseManagementResourceService,
    SystemLogoResourceService,
    DashboardResourceService,
    GovernanceResourceService,
    WorkflowResourceService,
    DashboardCategoryResourceService,
    ResponsibilityQueryResourceService,
    LicenseResourceService,
    UserGroupResourceService,
    ObjectInstanceFilterControllerService
];
