import { environment } from "src/environments/environment";
import WebSecureStorage from 'secure-web-storage';
import CryptoJS from 'crypto-js';

const secureStorage = new WebSecureStorage(localStorage, {
    hash: function hash(key) {
        key = CryptoJS.SHA256(key, environment.SECRET_KEY);
        return key.toString();
    },
    encrypt: function encrypt(data) {
        data = CryptoJS.AES.encrypt(data, environment.SECRET_KEY);
        data = data.toString();
        return data;
    },
    decrypt: function decrypt(data) {
        data = CryptoJS.AES.decrypt(data, environment.SECRET_KEY);
        data = data.toString(CryptoJS.enc.Utf8);
        return data;
    }
});

export function setEncryptedValues(key, value) {
    secureStorage.setItem(key, value);
}

export function getEncryptedValues(key) {
    return secureStorage.getItem(key);
}
