import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CACHE_APIS } from '@services/indexed-db.config';
import { IndexedDbService, ObjectStoreValue } from '@services/indexed-db.service';
import { isEqual } from 'lodash-es';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, mergeMap, switchMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})

export class CacheInterceptorService implements HttpInterceptor {

  constructor(
    private readonly indexedDbService: IndexedDbService
  ) { }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const objectStoreName = Object.keys(CACHE_APIS).find((store) =>
      CACHE_APIS[store].some((api) => req.url.includes(api.path) && req.method === api.method)
    );

    const requestURL = req.method === 'POST' ? `${req.urlWithParams}&paylod=${JSON.stringify(req.body)}` : req.urlWithParams;
    return this.indexedDbService.getDataFromIndexDB(objectStoreName || 'EtagAPIs', requestURL).pipe(
      switchMap((storedResponse: ObjectStoreValue) => {
        if (storedResponse && !storedResponse?.etag) {
          if (req.method === 'GET') {
            return of(new HttpResponse({
              body: storedResponse.data,
              status: 200
            }));
          }

          if (req.method === 'POST') {
            if (isEqual(req.body, storedResponse.payload)) {
              return of(new HttpResponse({
                body: storedResponse.data,
                status: 200
              }));
            }
          }
        }

        let modifiedReq = req;
        if (storedResponse?.etag) {
          modifiedReq = req.clone({
            setHeaders: { 'If-None-Match': storedResponse.etag }
          });
        }

        return next.handle(modifiedReq).pipe(
          mergeMap((event) => {
            if (event instanceof HttpResponse) {
              if (event.status === 304 && storedResponse) {
                return of(new HttpResponse({
                  body: storedResponse.data,
                  status: 200,
                  headers: event.headers
                }));
              }

              const etag = event.headers.get('ETag') || null;
              const payload = req.method === 'POST' ? req.body : null;
              const storeName = etag ? 'EtagAPIs' : objectStoreName ?? null;
              if (storeName) {
                if (req.method === 'GET' || (req.method === 'POST' && req.body?.filterGroups?.length === 0))
                  return this.indexedDbService.storeDataInsideIndexDB(storeName, requestURL, event.body, etag, payload).pipe(
                    catchError((error) => {
                      return of(event);
                    }),
                    map(() => event)
                  );
              }
            }
            return of(event);
          }),
          catchError((error: HttpErrorResponse) => {
            if (error.status === 304 && storedResponse) {
              return of(new HttpResponse({
                body: storedResponse.data,
                status: 200,
                headers: error.headers
              }));
            }
            return throwError(() => error);
          })
        );
      }),
      catchError(() => next.handle(req))
    );
  }
}
