import { <PERSON>tt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CookiesService } from '@services/cookies.service';
import { Observable } from 'rxjs';
import { ROTGenerator } from '../../rot-generator';

@Injectable({
  providedIn: 'root'
})

export class TenantIdInterceptorService implements HttpInterceptor {

  constructor(
    public router: Router,
    private readonly cookiesService: CookiesService
  ) {
  }

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    const isSystemConfigAPI = request.url.includes(ROTGenerator.encryptUrl('system-config'));
    const spaceId = this.cookiesService.getCookie('spaceId');
    if (spaceId) {
      let newRequest: HttpRequest<any> = request.clone({});
      if (!isSystemConfigAPI) {
        newRequest = request.clone({
          headers: request.headers.set('X-TENANT-ID', spaceId)
        });
      }
      return next.handle(newRequest);
    }
    return next.handle(request);
  }

  // Method for getting XSRF token from the domain;
  getXSRFCookie() {
    // Need to change once this is deployed in the security server
    return window.document.cookie;
  }

  getCSRF(): string {
    const name = 'XSRF-TOKEN=';
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      let char = cookie;
      while (char.startsWith(' ')) {
        char = char.substring(1);
      }
      if (char.indexOf(name) !== -1) {
        return char.substring(name.length, char.length);
      }
    }
    return '';
  }
}
