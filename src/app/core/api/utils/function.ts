/*
 * Generates a random number and returns
 */
export const getRandomNumber = (): number => {
  let arr = new Uint32Array(1)
  crypto.getRandomValues(arr)
  return arr[0] / (2**32)
}

export const citiesList =(translationService:any)=> [
  {name: translationService.transLables('theme_management_new_york','New York'), code: 'NY'},
  {name: translationService.transLables('theme_management_rome','Rome'), code: 'RM'},
  {name: translationService.transLables('theme_management_london','London'), code: 'LDN'},
  {name: translationService.transLables('theme_management_istanbul','Istanbul'), code: 'IST'},
  {name: translationService.transLables('theme_management_paris','Paris'), code: 'PRS'}
];
export const paymentOptions = (translationService:any)=>[
  {name: translationService.transLables('theme_management_option_1','Option 1'), value: 1},
  {name: translationService.transLables('theme_management_option_2','Option 2'), value: 2},
  {name: translationService.transLables('theme_management_option_3','Option 3'), value: 3}
];

export const representativesList = [
  {name: 'Amy Elsner', image: 'amyelsner.png'},
  {name: 'Anna Fali', image: 'annafali.png'},
  {name: 'Asiya Javayant', image: 'asiyajavayant.png'},
  {name: 'Bernardo Dominic', image: 'bernardodominic.png'},
  {name: 'Elwin Sharvill', image: 'elwinsharvill.png'},
  {name: 'Ioni Bowcher', image: 'ionibowcher.png'},
  {name: 'Ivan Magalhaes', image: 'ivanmagalhaes.png'},
  {name: 'Onyama Limba', image: 'onyamalimba.png'},
  {name: 'Stephen Shaw', image: 'stephenshaw.png'},
  {name: 'XuXue Feng', image: 'xuxuefeng.png'}
];

export const statusesList =(translationService:any)=> [
  {label: translationService.transLables('theme_management_unqualified','Unqualified'), value: 'unqualified'},
  {label: translationService.transLables('theme_management_qualified','Qualified'), value: 'qualified'},
  {label: translationService.transLables('theme_management_new','New'), value: 'new'},
  {label: translationService.transLables('theme_management_negotiation','Negotiation'), value: 'negotiation'},
  {label: translationService.transLables('theme_management_renewal','Renewal'), value: 'renewal'},
  {label: translationService.transLables('theme_management_proposal','Proposal'), value: 'proposal'}
];

