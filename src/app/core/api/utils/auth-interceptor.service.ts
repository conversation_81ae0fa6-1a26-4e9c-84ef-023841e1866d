import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Constants } from '@api/constants';
import { IndexedDbService } from '@services/indexed-db.service';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})

export class AuthInterceptorService implements HttpInterceptor {

  constructor(
    private readonly indexedDbService: IndexedDbService
  ) { }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          this.indexedDbService.clearAllObjectStores();
          const url = Constants.LOGIN;
          document.location.href = url;
        }
        return throwError(() => error);
      })
    );
  }
}
