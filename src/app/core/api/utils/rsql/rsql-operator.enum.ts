export enum RsqlOperator {
    EQUAL = '==',
    NOT_EQUAL = '!=',
    GREATER_THAN = '=gt=',
    GREATER_THAN_OR_EQUAL = '=ge=',
    LESS_THAN = '=lt=',
    LESS_THAN_OR_EQUAL = '=le=',
    IN = '=in=',
    NOT_IN = '=out=',
    IS_NULL = '=na=',
    NOT_NULL = '=nn=',
    LIKE = '=ke=',
    NOT_LIKE = '=nk=',
    IGNORE_CASE = '=ic=',
    IGNORE_CASE_LIKE = '=ik=',
    IGNORE_CASE_NOT_LIKE = '=ni=',
    BETWEEN = '=bt=',
    NOT_BETWEEN = '=nb=',
    LIKE_V2 = '=like=',
}
