import { RsqlCondition } from './rsql-condition';
import { RsqlConditionLinker } from './rsql-condition-linker.enum';

export class RsqlBuilder {
    private readonly queue: Array<any> = new Array();

    public addCondition(condition: RsqlCondition) {
        this.queue.push(condition);
        return this;
    }
    public addLinker(conditionLinker: RsqlConditionLinker) {
        this.queue.push(conditionLinker);
        return this;
    }

    public buildRsqlQuery(): string {
        let value = '';
        for (const obj of this.queue) {
            if (obj instanceof RsqlCondition) {
                const condition = obj ;
                value += condition.propertyName;
                value += condition.operator;
                if (condition.value) {
                    value += condition.value;
                }
            } else {
                const linker = obj as RsqlConditionLinker;
                value += linker;
            }
        }

        return value;
    }
}
