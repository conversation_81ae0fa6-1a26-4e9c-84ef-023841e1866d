import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ShareStateService {
  public userDetail: BehaviorSubject<any>;
  public tenant: BehaviorSubject<any>;
  public encyclopedia: BehaviorSubject<any>;
  public selectedJson: BehaviorSubject<any>;
  public connectionCreated: BehaviorSubject<any>;
  public connectionCreatedId: BehaviorSubject<any>;
  public languages: BehaviorSubject<any>;
  public selectedLanguage: BehaviorSubject<any>;
  public roleJson: BehaviorSubject<any>;
  public allTranslations: BehaviorSubject<Array<any>>;
  public userProfileImg: BehaviorSubject<any>;
  public defultLangugeKey: BehaviorSubject<any>;
  public langugeKeyValue: BehaviorSubject<any>;
  public selectedCalendar: BehaviorSubject<any>;
  public diagramJSON: BehaviorSubject<any>;
  public dialogsConfig: BehaviorSubject<any>;
  public defaultView: BehaviorSubject<any>;
  public showView: BehaviorSubject<any>;
  public publicDefaultView: BehaviorSubject<any>;
  public orgChartConfig: BehaviorSubject<any>;
  public logo: BehaviorSubject<any>;
  public navigationPanelCollapse: BehaviorSubject<any>;

  public isCollapsed: any = {
    myViews: true,
    myActions: true,
    encyclopedias: true,
    encycloSubmenu: true,
    administration: true,
  };
  item = new Subject<any>();
  attrDragItems = new Subject<any>();
  kpiFreq = new Subject<any>();
  compositeSelectedinstanceList = new Subject<any>();
  taskAccordionState = new Subject<string>();
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject(true);

  constructor() {
    this.userDetail = new BehaviorSubject(null);
    this.tenant = new BehaviorSubject({});
    this.encyclopedia = new BehaviorSubject({});
    this.selectedJson = new BehaviorSubject({});
    this.connectionCreated = new BehaviorSubject({});
    this.connectionCreatedId = new BehaviorSubject({});
    this.languages = new BehaviorSubject([]);
    this.allTranslations = new BehaviorSubject([]);
    this.selectedLanguage = new BehaviorSubject('');
    this.connectionCreatedId.asObservable();
    this.roleJson = new BehaviorSubject({});
    this.userProfileImg = new BehaviorSubject(null);
    this.defultLangugeKey = new BehaviorSubject({});
    this.langugeKeyValue = new BehaviorSubject([]);
    this.selectedCalendar = new BehaviorSubject(null);
    this.diagramJSON = new BehaviorSubject(null);
    this.dialogsConfig = new BehaviorSubject(null);
    this.showView = new BehaviorSubject(null);
    this.defaultView = new BehaviorSubject(null);
    this.publicDefaultView = new BehaviorSubject(null);
    this.orgChartConfig = new BehaviorSubject(null);
    this.logo = new BehaviorSubject(null);
    this.navigationPanelCollapse = new BehaviorSubject(null);
  }

  getTranslations(translations) {
    this.allTranslations = translations;
  }

  disableFields(actionType) {
    const fields = document.querySelectorAll('.gridster-item-content');
    if (actionType == 'view') {
      for (const field of Array.from(fields)) {
        field.setAttribute('disabled', 'disabled');
    }

    } else {
      for (const field of Array.from(fields)) {
        field.removeAttribute('disabled');
      }
    }
  }

  public onActionItemEnableSet(tempId) {
    this.item.next({tempId: tempId});
  }

  public onActionItemEnableGet(): Observable<any> {
    return this.item.asObservable();
  }

  public onActionArraySet(actions, numberPerRow) {
    this.item.next({actions, numberPerRow});
  }

  public onActionArrayGet(): Observable<any> {
    return this.item.asObservable();
  }

  setTabSorting(event) {
    let tabList = event.formTabTemplates.sort((a, b) => a.sequence - b.sequence);
    return tabList;
  }

  public setDataGridGroupRowValue(groupByRow) {
    this.item.next({
      groupByRow: groupByRow.value,
      groupByRowId: groupByRow.id
    });
  }

  public getDataGridGroupRowValue(): Observable<any> {
    return this.item.asObservable();
  }


  public checkFileUpload(value) {
    this.item.next({isFileUpload: value});
  }

  public getFileUploadCheck(): Observable<any> {
    return this.item.asObservable();
  }

  public setKpiReportingFrequency(id) {
    this.kpiFreq.next({reportingFrequencyDetail: id});
  }

  public getKpiReportingFrequency(): Observable<any> {
    return this.kpiFreq.asObservable();
  }

  public setcompositeSelectedinstanceList(val) {
    this.compositeSelectedinstanceList.next({selectedInstanceList: val})
  }

  public getcompositeSelectedinstanceList(): Observable<any> {
    return this.compositeSelectedinstanceList.asObservable()
  }

  setGridStyle(check, id) {
    if (!check) {
      setTimeout(() => {
        let grid = document.getElementById(id)
        let element = grid.querySelectorAll<HTMLElement>('.dx-datagrid-nowrap');
        for (const el of Array.from(element)) {
          el.style.bottom = '36px';
        }
      }, 300);
    } else {
      setTimeout(() => {
        let grid = document.getElementById(id)
        let element = grid.querySelectorAll<HTMLElement>('.dx-datagrid-nowrap');
        for (const el of Array.from(element)) {
          el.style.bottom = '0px';
        }
      }, 300);
    }
  }

  getFlagIcon(lang) {
    return lang.flagIcon;
  }

  serializeData(data, type) {
    if (type == 'stringify') {
      return typeof data == 'object' ? JSON.stringify(data) : data;
    } else {
      return typeof data == 'string' ? JSON.parse(data) : data;
    }
  }

  activateStimulsoftLicense(Stimulsoft) {
    Stimulsoft.Base.StiLicense.Key = environment.STI_LICENSE;
    Stimulsoft.StiOptions.WebServer.url = environment.STI_WEB_SERVER_URL;
  }

  public taskAccordionStateSet(tag) {
    this.taskAccordionState.next(tag)
  }

  public taskAccordionStateGet(): Observable<string> {
    return this.taskAccordionState.asObservable();
  }

  public setAutoExpandToogle(value) {
    this.item.next({
      autoExpand: value
    });
  }

  public getAutoExpandToogle(): Observable<any> {
    return this.item.asObservable();
  }

  public camelCasetoTitleCase(text) {
    const result = text.replace(/([A-Z])/g, " $1");
    const finalResult = result.charAt(0).toUpperCase() + result.slice(1);
    return finalResult
  }

  public camelCaseToSnakeCase(key) {
    const result = key.replace(/([A-Z])/g, " $1");
    return result.split(' ').join('_').toLowerCase();
  }

  public convertToSnakeCase(key){
    return key?.split(' ').join('_').toLowerCase();
  }

  public snakeCaseToTitleCase(key) {
    return key?.toLowerCase().split('_').map(item => item[0].toUpperCase() + item.slice(1)).join(" ")
  }

  setIsLoading(value) {
    this.isLoading.next(value)
  }

  getIsLoading() {
    return this.isLoading
  }


}
